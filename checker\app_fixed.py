#!/usr/bin/env python3
"""
修复版的Flask应用
"""
from flask import Flask, render_template, jsonify, request, send_file
import os
import sys
import traceback
from video_processor import VideoProcessor

# 配置
VIDEO_DIR = "../dataset_drone_video"
ANALYSIS_FILE = "../video_prefix_analysis.txt"
BEST_VIDEO_DIR = "../Best_video"
PROMPTS_FILE = "../drone_prompts.csv"

app = Flask(__name__)

# 全局变量声明
processor = None

def init_processor():
    """初始化视频处理器"""
    global processor
    try:
        processor = VideoProcessor(ANALYSIS_FILE, VIDEO_DIR, BEST_VIDEO_DIR, PROMPTS_FILE)
        return True
    except Exception as e:
        print(f"❌ 初始化VideoProcessor失败: {e}")
        traceback.print_exc()
        return False

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前评估状态"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        status = processor.get_status()
        return jsonify({"success": True, "data": status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_progress')
def sync_progress():
    """手动触发进度同步"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
            
        sync_result = processor.sync_with_best_video_dir()
        
        # 获取同步后的状态
        status = processor.get_status()
        sync_status = processor.get_sync_status()
        
        return jsonify({
            "success": True, 
            "data": {
                "sync_result": sync_result,
                "updated_status": status,
                "sync_status": sync_status
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_status')
def get_sync_status():
    """获取同步状态信息"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        sync_status = processor.get_sync_status()
        return jsonify({"success": True, "data": sync_status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/next_round')
def get_next_round():
    """获取下一轮比较的视频"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        comparison = processor.get_next_comparison()
        if comparison:
            return jsonify({"success": True, "data": comparison})
        else:
            return jsonify({"success": False, "error": "没有更多的比较"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/reset_current')
def reset_current_round():
    """重置当前轮次评估"""
    try:
        if not processor:
            return jsonify({"success": False, "error": "处理器未初始化"}), 500
        result = processor.reset_current_tournament()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/video/<path:filename>')
def serve_video(filename):
    """提供视频文件服务"""
    try:
        # 安全检查：确保文件名不包含路径遍历
        if '..' in filename or filename.startswith('/'):
            return "Invalid filename", 400
        
        video_path = os.path.join(VIDEO_DIR, filename)
        
        if not os.path.exists(video_path):
            return "Video not found", 404
        
        return send_file(video_path)
    except Exception as e:
        return f"Error serving video: {str(e)}", 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "API endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "Internal server error"}), 500

def main():
    """主函数"""
    print("=== 视频质量评估工具启动 ===")
    
    # 确保模板和静态文件目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # 初始化处理器
    print("1. 初始化视频处理器...")
    if not init_processor():
        print("❌ 初始化失败，退出程序")
        return False
    
    print(f"   ✅ 成功加载 {len(processor.prefix_groups)} 个前缀组")
    print(f"   ✅ 成功加载 {len(processor.prompts)} 个提示信息")
    
    # 执行进度同步
    print("\n2. 执行进度同步...")
    try:
        sync_result = processor.sync_with_best_video_dir()
        
        print(f"   📁 扫描Best_video文件: {sync_result['scanned_files']} 个")
        print(f"   ✅ 恢复已完成前缀: {sync_result['found_completed']} 个")
        print(f"   🗑️  移除缺失前缀: {sync_result['removed_missing']} 个")
        
        if sync_result['errors']:
            print(f"   ⚠️  错误: {len(sync_result['errors'])} 个")
            for error in sync_result['errors'][:3]:
                print(f"      - {error}")
    except Exception as e:
        print(f"   ⚠️  同步过程中出现错误: {e}")
    
    # 显示同步后的状态
    print("\n3. 显示当前状态...")
    try:
        status = processor.get_status()
        sync_status = processor.get_sync_status()
        
        print(f"   📊 总前缀数: {status['total_prefixes']}")
        print(f"   ✅ 已完成: {status['completed_prefixes']}")
        print(f"   📈 进度: {status['progress_percentage']:.1f}%")
        print(f"   🔄 同步状态: {'✅ 已同步' if sync_status['is_synced'] else '⚠️ 需要同步'}")
    except Exception as e:
        print(f"   ⚠️  获取状态时出现错误: {e}")
    
    # 启动Flask应用
    print(f"\n4. 启动Web服务器...")
    print(f"   🌐 访问地址: http://127.0.0.1:5000")
    print(f"   📁 视频目录: {os.path.abspath(VIDEO_DIR)}")
    print(f"   📁 最佳视频目录: {os.path.abspath(BEST_VIDEO_DIR)}")
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Flask应用启动失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
