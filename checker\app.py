from flask import Flask, render_template, jsonify, request, send_file
import os
import sys
from video_processor import VideoProcessor

# 添加父目录到路径，以便访问根目录的文件
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

app = Flask(__name__)

# 配置
VIDEO_DIR = "../dataset_drone_video"
ANALYSIS_FILE = "../video_prefix_analysis.txt"
BEST_VIDEO_DIR = "../Best_video"
PROMPTS_FILE = "../drone_prompts.csv"

# 全局变量声明
processor = None

def init_processor():
    """初始化视频处理器"""
    global processor
    processor = VideoProcessor(ANALYSIS_FILE, VIDEO_DIR, BEST_VIDEO_DIR, PROMPTS_FILE)

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前评估状态"""
    try:
        status = processor.get_status()
        return jsonify({"success": True, "data": status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/next_round')
def get_next_round():
    """获取下一轮比较的视频"""
    try:
        comparison = processor.get_next_comparison()
        if comparison:
            return jsonify({"success": True, "data": comparison})
        else:
            return jsonify({"success": False, "error": "没有更多的比较"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/select_winner', methods=['POST'])
def select_winner():
    """提交获胜视频选择"""
    try:
        data = request.get_json()
        winner_video = data.get('winner')
        
        if not winner_video:
            return jsonify({"success": False, "error": "未提供获胜视频"}), 400
        
        result = processor.submit_winner(winner_video)
        
        if "error" in result:
            return jsonify({"success": False, "error": result["error"]}), 400
        
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/reset_current')
def reset_current_round():
    """重置当前轮次评估"""
    try:
        result = processor.reset_current_tournament()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/reset_all')
def reset_all_evaluation():
    """重置所有评估状态"""
    try:
        # 删除状态文件
        if processor and os.path.exists(processor.state_file):
            os.remove(processor.state_file)

        # 重新初始化处理器
        init_processor()

        return jsonify({"success": True, "message": "所有评估状态已重置"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/video/<path:filename>')
def serve_video(filename):
    """提供视频文件服务"""
    try:
        # 安全检查：确保文件名不包含路径遍历
        if '..' in filename or filename.startswith('/'):
            return "Invalid filename", 400
        
        video_path = os.path.join(VIDEO_DIR, filename)
        
        if not os.path.exists(video_path):
            return "Video not found", 404
        
        return send_file(video_path)
    except Exception as e:
        return f"Error serving video: {str(e)}", 500

@app.route('/api/logs')
def get_logs():
    """获取评估日志"""
    try:
        if os.path.exists(processor.log_file):
            with open(processor.log_file, 'r', encoding='utf-8') as f:
                import json
                logs = json.load(f)
            return jsonify({"success": True, "data": logs})
        else:
            return jsonify({"success": True, "data": []})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/export_results')
def export_results():
    """导出评估结果"""
    try:
        status = processor.get_status()
        
        # 创建结果报告
        from datetime import datetime
        report = {
            "evaluation_summary": {
                "total_prefixes": status["total_prefixes"],
                "completed_prefixes": status["completed_prefixes"],
                "progress_percentage": status["progress_percentage"],
                "start_time": status.get("start_time"),
                "export_time": datetime.now().isoformat()
            },
            "completed_evaluations": processor.state.get("completed_prefixes", [])
        }
        
        # 添加日志数据
        if os.path.exists(processor.log_file):
            with open(processor.log_file, 'r', encoding='utf-8') as f:
                import json
                report["detailed_logs"] = json.load(f)
        
        return jsonify({"success": True, "data": report})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/skip_prefix', methods=['POST'])
def skip_prefix():
    """跳过当前前缀"""
    try:
        if processor.state.get("current_tournament"):
            tournament = processor.state["current_tournament"]
            processor.log_action("prefix_skipped", {
                "prefix": tournament["prefix"],
                "reason": "user_requested"
            })

            # 移动到下一个前缀
            processor.state["current_prefix_index"] += 1
            processor.state["current_tournament"] = None
            processor.save_state()

        return jsonify({"success": True, "message": "已跳过当前前缀"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_progress')
def sync_progress():
    """手动触发进度同步"""
    try:
        sync_result = processor.sync_with_best_video_dir()

        # 获取同步后的状态
        status = processor.get_status()
        sync_status = processor.get_sync_status()

        return jsonify({
            "success": True,
            "data": {
                "sync_result": sync_result,
                "updated_status": status,
                "sync_status": sync_status
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/sync_status')
def get_sync_status():
    """获取同步状态信息"""
    try:
        sync_status = processor.get_sync_status()
        return jsonify({"success": True, "data": sync_status})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "API endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "Internal server error"}), 500

if __name__ == '__main__':
    # 初始化处理器
    init_processor()

    # 确保模板和静态文件目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    print("视频质量评估工具启动中...")
    print(f"视频目录: {os.path.abspath(VIDEO_DIR)}")
    print(f"分析文件: {os.path.abspath(ANALYSIS_FILE)}")
    print(f"最佳视频目录: {os.path.abspath(BEST_VIDEO_DIR)}")
    print(f"总前缀数量: {len(processor.prefix_groups)}")

    # 执行进度同步
    print("\n正在同步评估进度...")
    sync_result = processor.sync_with_best_video_dir()

    if sync_result["errors"]:
        print("⚠️  同步过程中发现错误:")
        for error in sync_result["errors"]:
            print(f"   - {error}")

    print(f"📁 扫描Best_video文件: {sync_result['scanned_files']} 个")
    print(f"✅ 恢复已完成前缀: {sync_result['found_completed']} 个")
    print(f"🗑️  移除缺失前缀: {sync_result['removed_missing']} 个")

    if sync_result['updated_prefixes']:
        print(f"📝 更新的前缀: {', '.join(sync_result['updated_prefixes'][:5])}" +
              (f" 等{len(sync_result['updated_prefixes'])}个" if len(sync_result['updated_prefixes']) > 5 else ""))

    if sync_result['removed_prefixes']:
        print(f"🔄 重新加入评估的前缀: {', '.join(sync_result['removed_prefixes'][:5])}" +
              (f" 等{len(sync_result['removed_prefixes'])}个" if len(sync_result['removed_prefixes']) > 5 else ""))

    # 显示同步后的状态
    status = processor.get_status()
    sync_status = processor.get_sync_status()

    print(f"\n📊 同步后状态:")
    print(f"   已完成: {status['completed_prefixes']}/{status['total_prefixes']} ({status['progress_percentage']:.1f}%)")
    print(f"   同步状态: {'✅ 已同步' if sync_status['is_synced'] else '⚠️ 需要同步'}")

    if not sync_status['is_synced']:
        print(f"   Best_video文件数: {sync_status['best_video_files_count']}")
        print(f"   状态记录数: {sync_status['state_completed_count']}")

    app.run(debug=True, host='0.0.0.0', port=5000)
