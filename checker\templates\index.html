<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频质量评估工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>

<body>
    <div class="container">
        <header>
            <h1>视频质量评估工具</h1>
            <div class="progress-info">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">加载中...</div>
            </div>
        </header>

        <main>
            <!-- 状态显示区域 -->
            <div class="status-panel" id="statusPanel">
                <div class="current-info">
                    <h3 id="currentPrefix">当前前缀: 加载中...</h3>
                    <p id="roundInfo">轮次信息: 加载中...</p>
                </div>
                <div class="controls">
                    <button id="playAllBtn" class="control-btn">播放所有</button>
                    <button id="pauseAllBtn" class="control-btn">暂停所有</button>
                    <button id="skipBtn" class="control-btn skip-btn">跳过当前前缀</button>
                    <button id="resetBtn" class="control-btn reset-btn">重置本轮评估</button>
                    <button id="syncBtn" class="control-btn sync-btn">同步进度</button>
                </div>
            </div>

            <!-- 提示信息显示区域 -->
            <div class="prompt-panel" id="promptPanel" style="display: none;">
                <h4>🎯 生成提示 (Generation Prompt)</h4>
                <p id="promptText">加载中...</p>
            </div>

            <!-- 视频比较区域 -->
            <div class="video-grid" id="videoGrid">
                <div class="video-item" data-video-index="0">
                    <video id="video0" controls loop>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div class="video-info">
                        <p class="video-name" id="videoName0">视频 1</p>
                        <button class="select-btn" data-video-index="0">选择此视频</button>
                    </div>
                </div>

                <div class="video-item" data-video-index="1">
                    <video id="video1" controls loop>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div class="video-info">
                        <p class="video-name" id="videoName1">视频 2</p>
                        <button class="select-btn" data-video-index="1">选择此视频</button>
                    </div>
                </div>

                <div class="video-item" data-video-index="2">
                    <video id="video2" controls loop>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div class="video-info">
                        <p class="video-name" id="videoName2">视频 3</p>
                        <button class="select-btn" data-video-index="2">选择此视频</button>
                    </div>
                </div>

                <div class="video-item" data-video-index="3">
                    <video id="video3" controls loop>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div class="video-info">
                        <p class="video-name" id="videoName3">视频 4</p>
                        <button class="select-btn" data-video-index="3">选择此视频</button>
                    </div>
                </div>
            </div>

            <!-- 完成状态显示 -->
            <div class="completion-panel" id="completionPanel" style="display: none;">
                <h2>🎉 评估完成！</h2>
                <p id="completionMessage">所有视频评估已完成。</p>
                <div class="completion-stats">
                    <p>总计处理前缀: <span id="totalProcessed">0</span></p>
                    <p>最佳视频已保存到 Best_video 目录</p>
                </div>
                <button id="exportBtn" class="control-btn">导出结果</button>
            </div>

            <!-- 加载状态 -->
            <div class="loading-panel" id="loadingPanel">
                <div class="spinner"></div>
                <p>加载中...</p>
            </div>
        </main>

        <footer>
            <div class="help-info">
                <h4>使用说明:</h4>
                <ul>
                    <li>观看视频并选择质量最佳的一个</li>
                    <li>系统将自动进行淘汰赛，最多5轮比较</li>
                    <li>单个视频的前缀将自动标记为最佳</li>
                    <li>最佳视频将自动复制到 Best_video 目录</li>
                </ul>

                <h4>快捷键:</h4>
                <ul>
                    <li><kbd>1-4</kbd>: 选择对应位置的视频</li>
                    <li><kbd>空格</kbd>: 播放/暂停所有视频</li>
                    <li><kbd>R</kbd>: 播放所有视频</li>
                    <li><kbd>S</kbd>: 暂停所有视频</li>
                    <li><kbd>N</kbd>: 跳过当前前缀</li>
                </ul>
            </div>
        </footer>
    </div>

    <!-- 确认对话框 -->
    <div class="modal" id="confirmModal" style="display: none;">
        <div class="modal-content">
            <h3>确认操作</h3>
            <p id="confirmMessage">确定要执行此操作吗？</p>
            <div class="modal-buttons">
                <button id="confirmYes" class="control-btn">确定</button>
                <button id="confirmNo" class="control-btn">取消</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>

</html>